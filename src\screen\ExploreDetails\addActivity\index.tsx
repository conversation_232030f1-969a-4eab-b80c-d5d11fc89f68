
import { WidgetIcon } from '@/components/icons';
import { BreadcrumbItem, Breadcrumbs } from '@heroui/react';
import ActivitiesCard from './ActivitiesCard';

const AddActivity = ({ onBack }: { onBack?: () => void }) => {
  return (
    <div className="bg-white rounded-xl p-4">
      <Breadcrumbs size="lg">
        <BreadcrumbItem>
          <WidgetIcon />
        </BreadcrumbItem>
        <BreadcrumbItem onClick={onBack} className="text-subtitle">
          Activities
        </BreadcrumbItem>
        <BreadcrumbItem>Experiences</BreadcrumbItem>
      </Breadcrumbs>

      <div className="text-gray-600 mt-4">
        <div className="space-y-4">
          <ActivitiesCard onClick={onBack}/>
        </div>
      </div>
    </div>
  );
};

export default AddActivity;
