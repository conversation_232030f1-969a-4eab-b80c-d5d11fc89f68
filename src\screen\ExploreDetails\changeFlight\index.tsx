import FlightFilter from '@/components/globalComponents/Filter/FlightFilter';
import DepartingFlight from '@/components/globalComponents/Itinerary/DepartingFlight';
import { MagicStickIcon, SortIcon, WidgetIcon } from '@/components/icons';
import { BreadcrumbItem, Breadcrumbs, Button } from '@heroui/react';
import { useState } from 'react';

const ChangeFlight = ({ onBack }: { onBack?: () => void }) => {
  const flights = [
    {
      airlineLogo: '/logos/indigo.png',
      airlineName: 'IndiGo',
      airlineType: 'Out bond',
      departureTime: '16:40',
      arrivalTime: '17:55',
      departureDate: 'Thu, 25 Feb',
      arrivalDate: 'Thu, 25 Feb',
      departureCity: 'London',
      arrivalCity: 'Munich',
      departureAirport: 'London Airport (VTZ)',
      arrivalAirport: 'Munich (HYD)',
      duration: '1 hr 15 min',
      aircraft: 'Airbus A321neo, 6E 783',
      price: '$112',
    },
    {
      airlineLogo: '/logos/airindia.png',
      airlineName: 'Air India',
      airlineType: 'Out bond',
      departureTime: '09:10',
      arrivalTime: '12:45',
      departureDate: 'Fri, 26 Feb',
      arrivalDate: 'Fri, 26 Feb',
      departureCity: 'Paris',
      arrivalCity: 'Berlin',
      departureAirport: 'Paris CDG',
      arrivalAirport: 'Berlin TXL',
      duration: '2 hr 35 min',
      aircraft: 'Boeing 737 MAX',
      price: '$150',
    },
  ];

  const [filterOpen, setFilterOpen] = useState(false);
  return (
    <div className="bg-white rounded-xl p-4">
      <Breadcrumbs size="lg">
        <BreadcrumbItem>
          <WidgetIcon />
        </BreadcrumbItem>
        <BreadcrumbItem onClick={onBack} className="text-subtitle">
          Flight
        </BreadcrumbItem>
        <BreadcrumbItem>PAT-VTZ</BreadcrumbItem>
      </Breadcrumbs>
      <div className="flex items-center justify-between my-3">
        <h2 className="text-xl font-semibold">Top departing flight</h2>
        <div className="flex flex-row gap-4">
          <Button
            color="primary"
            variant="light"
            startContent={<MagicStickIcon isAnimation={false} />}
          >
            Check Price History
          </Button>
          <div
            className="flex flex-row items-center gap-2 cursor-pointer"
            onClick={() => setFilterOpen(true)}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setFilterOpen(true);
              }
            }}
          >
            <SortIcon isAnimation={false} className="text-default-Secondary" />
            <p className="text-sm font-medium text-default-Secondary">Filter</p>
          </div>
        </div>
      </div>
      <div className="text-gray-600">
        <div className="space-y-4">
          {flights.map((f, i) => (
            <DepartingFlight
              key={i}
              id={`flight-${i}`}
              {...f}
              onClick={onBack}
            />
          ))}
        </div>
      </div>

      {/* Filter Modal */}
      <FlightFilter
        open={filterOpen}
        onClose={() => setFilterOpen(false)}
        onApply={() => {
          // Handle filter application logic here
          setFilterOpen(false);
        }}
      />
    </div>
  );
};

export default ChangeFlight;
