import {
  Bread<PERSON><PERSON>bItem,
  Breadcrumbs,
  Chip,
  Modal,
  ModalContent,
  ModalBody,
  useDisclosure,
} from '@heroui/react';
import { useState } from 'react';

import PrimaryFilterSkeleton from '@/components/loaders/PrimaryFilterSkeleton';
import PrimaryFilter from '@/components/globalComponents/primaryFilter';
import ChatSkeleton from '@/components/loaders/ChatSkeleton';
import { HeartIcon, DownloadIcon, ShareIcon } from '@/components/icons';
import Image from 'next/image';
import TabsSection from './tabsSection.tsx';
import ChatWithShasa from '@/components/globalComponents/ChatWithShasa';
import ChangeFlight from './changeFlight';
import BookThisPlan from '@/components/globalComponents/BookThisPlan';
import AddActivity from './addActivity';

interface DiscoverPageProps {
  isLoading?: boolean;
  loadingComponents?: {
    filter?: boolean;
    chat?: boolean;
    recommendations?: boolean;
  };
  location: string;
  title: string;
}
const ExploreDetailsPage = ({
  isLoading = false,
  loadingComponents = {},
  location,
  title,
}: DiscoverPageProps) => {
  // State to manage which view to show
  const [showChangeFlight, setShowChangeFlight] = useState(false);
  const [showAddActivityFlight, setShowAddActivityFlight] = useState(false);

  // Modal state for Book This Plan
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();

  // Use the loading states directly from props
  const componentLoading = {
    filter: loadingComponents.filter ?? isLoading,
    chat: loadingComponents.chat ?? isLoading,
    recommendations: loadingComponents.recommendations ?? isLoading,
  };

  // Function to handle switching to ChangeFlight view
  const handleChangeFlightClick = () => {
    setShowChangeFlight(true);
  };

  // Function to handle going back to TabsSection view
  const handleBackToTabs = () => {
    setShowChangeFlight(false);
    setShowAddActivityFlight(false);
  };

  // Function to handle switching to Add Activity view
  const handleAddActivityClick = () => {
    setShowAddActivityFlight(true);
    setShowChangeFlight(false);
  };

  // Function to handle opening the Book This Plan modal
  const handleBookThisPlanClick = () => {
    onOpen();
  };
  return (
    <div className="p-3 sm:p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      <div className="">
        <Breadcrumbs size="lg">
          <BreadcrumbItem href="/explore">Explore</BreadcrumbItem>
          <BreadcrumbItem>{location}</BreadcrumbItem>
          <BreadcrumbItem>{title}</BreadcrumbItem>
        </Breadcrumbs>
        <div className="flex flex-row items-center justify-between">
          <div className="text-2xl sm:text-4xl font-semibold">
            Historic Germany
          </div>
          <div className="flex flex-row items-center gap-3">
            <button
              type="button"
              onClick={handleBookThisPlanClick}
              className="px-5 py-2 rounded-full btn-gradient text-white text-sm font-semibold shadow-md hover:opacity-90 transition cursor-pointer"
            >
              Book this Plan
            </button>

            <Chip
              color="primary"
              variant="flat"
              size="md"
              className="font-semibold h-[40px]"
            >
              <HeartIcon size={20} />
            </Chip>
            <Chip
              color="primary"
              variant="flat"
              size="md"
              className="font-semibold h-[40px]"
            >
              <DownloadIcon size={20} />
            </Chip>

            <Chip
              color="primary"
              variant="flat"
              size="md"
              className="font-semibold h-[40px]"
            >
              <ShareIcon size={20} />
            </Chip>
          </div>
        </div>
      </div>

      {/* Primary Filter Section */}
      <div className="py-3 sm:py-4">
        {componentLoading.filter ? (
          <PrimaryFilterSkeleton />
        ) : (
          <PrimaryFilter />
        )}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-6 gap-3 sm:gap-4 flex-1 min-h-0">
        {/* Chat Section */}
        <div className="lg:col-span-2 order-2 lg:order-1 md:h-[calc(100vh-275px)]  md:overflow-y-auto custom-scrollbar">
          {componentLoading.chat ? <ChatSkeleton /> : <ChatWithShasa />}
          <div className="mt-4">
            <p className="text-xl font-bold mb-3">map</p>
            <Image
              src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/map.svg"
              alt="map"
              width={200}
              height={300}
              className="w-full h-[350px] object-cover rounded-xl"
            />
          </div>
        </div>

        {/* TabsSection Section */}
        <div className="lg:col-span-4 order-1 lg:order-2 md:h-[calc(100vh-275px)]  md:overflow-y-scroll custom-scrollbar ">
          {showChangeFlight ? (
            <ChangeFlight onBack={handleBackToTabs} />
          ) : showAddActivityFlight ? (
            <AddActivity onBack={handleBackToTabs} />
          ) : (
            <TabsSection
              location={location}
              title={title}
              onChangeFlightClick={handleChangeFlightClick}
              onAddActivityClick={handleAddActivityClick}
            />
          )}
        </div>
      </div>

      {/* Full Screen Book This Plan Modal */}
      <Modal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        size="full"
        classNames={{
          base: 'm-0 max-w-none max-h-none',
          wrapper: 'w-full h-full',
          body: 'p-0',
        }}
        hideCloseButton
        isDismissable
        isKeyboardDismissDisabled={false}
      >
        <ModalContent className="h-full">
          <ModalBody className="h-full p-0">
            <BookThisPlan onClose={onClose} />
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ExploreDetailsPage;
