'use client';

import React from 'react';
import { BsImageAlt } from 'react-icons/bs';
import type { Flight } from '@/types/flight';
import { Button } from '@heroui/react';
import { GiMeal } from "react-icons/gi";
import { BsFillLuggageFill } from "react-icons/bs";
import {
  FaPlaneDeparture,
  FaPlaneArrival,
  FaClock,
  FaTag,
} from 'react-icons/fa';
import ActivitiesCard from '@/components/globalComponents/Itinerary/ActivitiesCard';

type Props = {
  flight: Flight;
  onAddActivityClick?: () => void;
};

export default function ActivityType({ flight, onAddActivityClick }: Props) {
  const flightInfo = [
    {
      title: 'Add Meal',
      description: 'Explore diner on your own or book a table ',
      icon: <GiMeal className="z-10 text-black" size={20} />,
      Item: [1, 2],
    },
    {
      title: 'Add Exploration',
      description: 'Spend they day at leisure or add an activity for the day',
      icon: <BsImageAlt className="z-10 text-black" size={20} />,
      Item: [1],
    },
    {
      title: 'Add luggage',
      description: '1 hr 15 min • Non Stop',
      icon: <BsFillLuggageFill  className="z-10 text-black" size={20} />,
      Item: [],
    },
  ];

  return (
    <div className="w-full space-y-3">
      {flightInfo.map((item, idx) => (
        <div key={idx}>
          {/* Header */}
          <div className="flex flex-row gap-4">
            <div className="min-w-[78px] text-center">
              <Button
                color="primary"
                variant="flat"
                size="md"
                onPress={onAddActivityClick}
                className="text-lg text-subtitle"
              >
                +
              </Button>
            </div>

            <div className="w-full">
              <div className="flex flex-row  items-center">
                <div
                  className={`flex flex-row w-full items-center border border-gray-300 rounded-lg `}
                >
                  <div className="w-full">
                    <div className="flex items-center w-full text-sm px-4 py-2">
                      {/* <BsImageAlt className="z-10 text-black" size={20} /> */}
                      {item.icon}
                      <span className="text-subtitle mr-3 ml-3 font-bold text-sm">
                        Activities
                      </span>
                      <span className="truncate text-base text-subtitle font-medium ">
                        {item.title}
                      </span>
                      <span className="mx-2 text-gray-400">|</span>
                      <span className="text-base text-black">
                        {item.description}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* flight info */}
          <div className='mt-4'>
            {item.Item.map((item, idx) => (
              <ActivitiesCard flight={flight} />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
