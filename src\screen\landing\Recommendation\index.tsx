'use client';

import {
  useEffect,
  useState,
  useRef,
  useCallback,
  useLayoutEffect,
} from 'react';

import { SortIcon } from '@/components/icons';

import RecommendationCard from './RecommendationCard';
import RecommendationFilter from '@/components/globalComponents/Filter/RecommendationFilter';

/**
 * ========================================
 * RECOMMENDATION COMPONENT
 * ========================================
 *
 * A responsive, auto-cycling recommendation display component that:
 *
 * 🎯 KEY FEATURES:
 * - Automatically calculates optimal number of visible cards based on container height
 * - Auto-cycles through recommendations every 4 seconds
 * - Pauses cycling on hover for better UX
 * - Responsive design that adapts to different screen sizes
 * - Robust DOM measurement with retry logic and multiple observers
 *
 * 🔧 TECHNICAL IMPLEMENTATION:
 * - Uses ResizeObserver, IntersectionObserver, and MutationObserver for reliable measurements
 * - Implements exponential backoff retry logic for DOM readiness
 * - Debounced resize handling to prevent excessive recalculations
 * - useLayoutEffect for synchronous DOM measurements
 *
 * 📋 INTEGRATION CHECKLIST FOR API TEAM:
 *
 * 1. REPLACE STATIC DATA:
 *    - Replace the 'recommendations' array with API data
 *    - Expected endpoint: GET /api/recommendations
 *    - Response format: Array<Recommendation> (see interface in RecommendationCard.tsx)
 *
 * 2. ADD LOADING STATES:
 *    - Add loading spinner while fetching data
 *    - Handle empty state when no recommendations available
 *    - Consider skeleton loading for better UX
 *
 * 3. ERROR HANDLING:
 *    - Add error boundary for API failures
 *    - Implement retry logic for failed requests
 *    - Show fallback content on errors
 *
 * 4. PERFORMANCE CONSIDERATIONS:
 *    - Implement pagination for large datasets
 *    - Consider virtual scrolling for many items
 *    - Add image lazy loading if not using Next.js Image
 *
 * 5. ACCESSIBILITY:
 *    - Add ARIA labels for screen readers
 *    - Implement keyboard navigation
 *    - Add reduced motion support
 *
 * 6. ANALYTICS (OPTIONAL):
 *    - Track card views and interactions
 *    - Monitor auto-cycling engagement
 *    - A/B test cycling intervals
 *
 * 🎨 DESIGN SYSTEM CONSTANTS:
 * - Card height: 280px (adjust in calculateVisibleCards if design changes)
 * - Card gap: 8px
 * - Auto-cycle interval: 4000ms
 * - Hover pause: enabled
 *
 * 📱 RESPONSIVE BEHAVIOR:
 * - Automatically shows 1-5+ cards based on available height
 * - Maintains aspect ratios across different screen sizes
 * - Handles orientation changes smoothly
 *
 * ⚠️  IMPORTANT NOTES:
 * - Do not modify the DOM measurement logic without thorough testing
 * - The retry mechanism is crucial for navigation scenarios
 * - Observer cleanup is essential to prevent memory leaks
 *
 * 🧪 TESTING RECOMMENDATIONS:
 * - Test on various screen sizes and orientations
 * - Verify behavior during navigation transitions
 * - Test with different numbers of recommendations (1, 3, 5, 10+)
 * - Validate hover pause/resume functionality
 * - Check performance with large datasets
 */

/**
 * Recommendation Component
 *
 * A responsive recommendation cards display with auto-cycling functionality.
 * Automatically calculates the optimal number of visible cards based on available space.
 *
 * Integration Notes for API Team:
 * - Replace the static 'recommendations' array with API data
 * - Ensure API response matches the Recommendation interface:
 *   {
 *     title: string;
 *     duration: string;
 *     location: string;
 *     tags: string;
 *     image: string;
 *     badge: string;
 *   }
 * - Add loading and error states as needed
 * - Consider implementing pagination for large datasets
 */
const Recommendation = () => {
  // State for managing visible cards and cycling
  const [visibleCards, setVisibleCards] = useState(3);
  const [currentStartIndex, setCurrentStartIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  // Refs for DOM measurements and cleanup
  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [filterOpen, setFilterOpen] = useState(false);

  // TODO: Replace with API data
  // Expected API endpoint: GET /api/recommendations
  // Response format: Array<Recommendation>
  const recommendations = [
    {
      title: 'Town Browsing',
      duration: '4 nights / 5 days',
      location: 'Aspen',
      tags: 'Activities, Explore, Leisure, Family',
      image:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/winterLuxury.svg',
      badge: 'badge',
    },
    {
      title: 'Mountain Retreat',
      duration: '3 nights / 4 days',
      location: 'Colorado',
      tags: 'Nature, Relax, Hiking',
      image:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/townBrowsing.svg',
      badge: 'Top Rated',
    },
    {
      title: 'City Lights',
      duration: '2 nights / 3 days',
      location: 'New York',
      tags: 'Urban, Nightlife, Shopping',
      image:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/antientWalk.svg',
      badge: 'Hot',
    },
    {
      title: 'Beach Paradise',
      duration: '5 nights / 6 days',
      location: 'Hawaii',
      tags: 'Beach, Relaxation, Water Sports',
      image:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/winterLuxury.svg',
      badge: 'Popular',
    },
    {
      title: 'Cultural Journey',
      duration: '6 nights / 7 days',
      location: 'Japan',
      tags: 'Culture, History, Food',
      image:
        'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/townBrowsing.svg',
      badge: 'New',
    },
  ];

  /**
   * Calculate optimal number of visible cards based on screen size and container space
   * Simplified responsive approach that works better with CSS Grid layouts
   *
   * @param retryCount - Internal retry counter (starts at 0)
   */
  const calculateVisibleCards = useCallback(
    (retryCount = 0) => {
      // Use window dimensions for more reliable responsive behavior
      const windowWidth = window.innerWidth;

      // Responsive card count based on screen size and orientation
      let newVisibleCards = 3; // Default for desktop

      if (windowWidth < 640) {
        // Mobile: Show fewer cards, more space per card
        newVisibleCards = Math.min(2, recommendations.length);
      } else if (windowWidth < 1024) {
        // Tablet: Medium number of cards
        newVisibleCards = Math.min(3, recommendations.length);
      } else if (containerRef.current && headerRef.current) {
        // Desktop: Calculate based on available height
        // Force a reflow to ensure accurate measurements
        // eslint-disable-next-line no-unused-expressions
        containerRef.current.offsetHeight;
        // eslint-disable-next-line no-unused-expressions
        headerRef.current.offsetHeight;

        const containerHeight = containerRef.current.clientHeight;
        const headerHeight = headerRef.current.clientHeight;

        // Validate DOM measurements are ready
        if (containerHeight === 0 || headerHeight === 0) {
          if (retryCount < 5) {
            retryTimeoutRef.current = setTimeout(
              () => calculateVisibleCards(retryCount + 1),
              100 * (retryCount + 1)
            );
          }
          return;
        }

        // Design system constants
        const viewAllButtonHeight = 24;
        const padding = 16;
        const marginTop = 4;
        const cardGap = 8;
        const estimatedCardHeight = 120; // More conservative estimate

        // Calculate available space for cards
        const availableHeight =
          containerHeight -
          headerHeight -
          viewAllButtonHeight -
          padding -
          marginTop;

        // Calculate how many cards can fit
        const maxPossibleCards = Math.floor(
          (availableHeight + cardGap) / (estimatedCardHeight + cardGap)
        );

        newVisibleCards = Math.max(
          1,
          Math.min(maxPossibleCards, recommendations.length, 5) // Cap at 5 for UX
        );
      }

      setVisibleCards(prev => {
        if (prev !== newVisibleCards) {
          setCurrentStartIndex(0); // Reset to start when card count changes
          return newVisibleCards;
        }
        return prev;
      });
    },
    [recommendations.length]
  );

  /**
   * Initial calculation on component mount
   * Uses layoutEffect for synchronous DOM measurements before paint
   */
  useLayoutEffect(() => {
    // Small delay to ensure CSS is fully applied
    const timer = setTimeout(() => {
      calculateVisibleCards();
    }, 0);

    return () => clearTimeout(timer);
  }, [calculateVisibleCards]);

  /**
   * Set up responsive behavior with simplified, more reliable resize handling
   */
  useEffect(() => {
    // Simplified debounced resize handler
    const handleResize = () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      resizeTimeoutRef.current = setTimeout(() => {
        calculateVisibleCards();
      }, 100); // Faster response for better UX
    };

    // Set up ResizeObserver for container size changes
    if (containerRef.current && typeof ResizeObserver !== 'undefined') {
      resizeObserverRef.current = new ResizeObserver(() => {
        // Immediate recalculation for container changes
        calculateVisibleCards();
      });

      resizeObserverRef.current.observe(containerRef.current);
    }

    // Handle visibility changes (tab switching, etc.)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, recalculate dimensions
        setTimeout(() => calculateVisibleCards(), 50);
      }
    };

    // Listen for window resize and orientation changes
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [calculateVisibleCards]);

  /**
   * Auto-cycling functionality for recommendations
   *
   * Features:
   * - Automatically cycles through cards every 4 seconds
   * - Pauses on hover for better user experience
   * - Only activates when there are more cards than visible slots
   * - Loops back to start when reaching the end
   *
   * Integration Notes:
   * - Adjust interval timing (4000ms) based on UX requirements
   * - Consider adding user preference to disable auto-cycling
   */
  useEffect(() => {
    if (recommendations.length <= visibleCards || isHovered) {
      return undefined;
    }

    const interval = setInterval(() => {
      setCurrentStartIndex(prev => {
        const maxStartIndex = recommendations.length - visibleCards;
        return prev >= maxStartIndex ? 0 : prev + 1;
      });
    }, 4000); // 4 second intervals for comfortable reading

    return () => clearInterval(interval);
  }, [visibleCards, recommendations.length, isHovered]);

  // Get current slice of recommendations to display
  const displayedRecommendations = recommendations.slice(
    currentStartIndex,
    currentStartIndex + visibleCards
  );

  /**
   * Recalculate dimensions when displayed recommendations change
   * Ensures accurate measurements after DOM updates
   */
  useEffect(() => {
    const timer = setTimeout(() => {
      calculateVisibleCards();
    }, 100); // Faster response for better UX

    return () => clearTimeout(timer);
  }, [displayedRecommendations.length, calculateVisibleCards]);

  return (
    <div
      ref={containerRef}
      className="bg-white h-full px-4 py-2 rounded-xl flex flex-col overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        ref={headerRef}
        className="flex flex-row items-center justify-between flex-shrink-0"
      >
        <div>
          <p className="text-lg font-bold">Recommendation</p>
        </div>
        <div
          className="flex flex-row items-center gap-2 cursor-pointer"
          onClick={() => setFilterOpen(true)}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setFilterOpen(true);
            }
          }}
        >
          <SortIcon isAnimation={false} className="text-default-Secondary" />
          <p className="text-sm font-medium text-default-Secondary">Filter</p>
        </div>
      </div>
      <div className="flex-1 flex flex-col justify-between min-h-0 overflow-hidden">
        <RecommendationCard
          recommendations={displayedRecommendations}
          showViewAll
        />
      </div>
      {/* Filter Modal */}
      <RecommendationFilter
        open={filterOpen}
        onClose={() => setFilterOpen(false)}
        onApply={() => {
          // Handle filter application logic here
          setFilterOpen(false);
        }}
      />
    </div>
  );
};

export default Recommendation;
